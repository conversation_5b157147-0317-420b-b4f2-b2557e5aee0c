// Example DB queries with ReturnType pattern
// Shows how to create reusable query functions and their types

import { eq } from 'drizzle-orm';
import { z } from 'zod/v4';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';

// Zod schemas for query result validation
// Ensures runtime type safety and prevents DB schema drift issues
const userSchema = z.object({
	id: z.string(),
	username: z.string(),
	passwordHash: z.string()
});

const userProfileSchema = z.object({
	id: z.string(),
	username: z.string(),
	age: z.number().nullable()
});

const activeUserSchema = z.object({
	user: z.object({
		id: z.string(),
		username: z.string()
	}),
	lastSession: z.date()
});

// Query functions
export async function getUserByUsername(username: string) {
	const [user] = await db.select().from(table.user).where(eq(table.user.username, username));
	if (!user) return null;
	return userSchema.parse(user);
}

export async function getUserProfile(userId: string) {
	const [result] = await db
		.select({
			id: table.user.id,
			username: table.user.username,
			age: table.user.age
		})
		.from(table.user)
		.where(eq(table.user.id, userId));
	if (!result) return null;
	return userProfileSchema.parse(result);
}

export async function getActiveUsers() {
	const results = await db
		.select({
			user: {
				id: table.user.id,
				username: table.user.username
			},
			lastSession: table.session.expiresAt
		})
		.from(table.user)
		.innerJoin(table.session, eq(table.user.id, table.session.userId))
		.where(eq(table.session.expiresAt, new Date()));
	return activeUserSchema.array().parse(results);
}
