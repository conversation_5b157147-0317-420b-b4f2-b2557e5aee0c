// Example DB queries with ReturnType pattern
// Shows how to create reusable query functions and their types

import { eq } from 'drizzle-orm';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';

// Query functions
export async function getUserByUsername(username: string) {
	const [user] = await db.select().from(table.user).where(eq(table.user.username, username));
	return user ?? null;
}

export async function getUserProfile(userId: string) {
	const [result] = await db
		.select({
			id: table.user.id,
			username: table.user.username,
			age: table.user.age
		})
		.from(table.user)
		.where(eq(table.user.id, userId));
	return result ?? null;
}

export async function getActiveUsers() {
	return db
		.select({
			user: {
				id: table.user.id,
				username: table.user.username
			},
			lastSession: table.session.expiresAt
		})
		.from(table.user)
		.innerJoin(table.session, eq(table.user.id, table.session.userId))
		.where(eq(table.session.expiresAt, new Date()));
}
