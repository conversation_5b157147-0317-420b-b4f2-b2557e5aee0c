import { z } from 'zod/v4';

/**
 * Type-safe FormData helper with Zod validation
 * Combines form data extraction and validation in one step
 * Automatically throws validation errors, eliminating need for manual safeParse
 */
export function getFormData<T extends z.ZodTypeAny>(formData: FormData, schema: T): z.infer<T> {
	const entries: Record<string, string> = {};
	for (const [key, value] of formData) {
		if (typeof value === 'string') {
			entries[key] = value;
		}
	}
	return schema.parse(entries);
}

/**
 * Legacy helper for cases where you need raw form data without validation
 * Consider using getFormData with schema instead
 */
export function getRawFormData(formData: FormData): Record<string, string> {
	const entries: Record<string, string> = {};
	for (const [key, value] of formData) {
		if (typeof value === 'string') {
			entries[key] = value;
		}
	}
	return entries;
}