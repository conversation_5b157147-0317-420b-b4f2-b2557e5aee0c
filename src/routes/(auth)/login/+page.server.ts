import { hash, verify } from '@node-rs/argon2';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { fail, redirect } from '@sveltejs/kit';
import { eq } from 'drizzle-orm';
import { z } from 'zod/v4';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import { getFormData } from '$lib/server/form-utils';
import { ACTION_NAMES, type ActionName } from '$lib/types/constants';
import type { Actions, PageServerLoad } from './$types';

interface PostgresError {
	code: string;
}

type ActionResult = { success: true; redirect: string } | { success: false; message: string };

const loginSchema = z.object({
	username: z
		.string()
		.min(3, 'Username must be at least 3 characters')
		.max(31, 'Username must be at most 31 characters')
		.regex(
			/^[a-z0-9_-]+$/,
			'Username can only contain lowercase letters, numbers, hyphens, and underscores'
		),
	password: z
		.string()
		.min(6, 'Password must be at least 6 characters')
		.max(255, 'Password must be at most 255 characters')
});

const registerSchema = loginSchema; // Same validation rules

export const load: PageServerLoad = async (event) => {
	if (event.locals.user) {
		return redirect(302, '/dashboard');
	}
	return {};
};

// Dynamic action handlers for better DRY code
const actionHandlers: Record<ActionName, (event: Parameters<Actions['default']>[0]) => Promise<any>> = {
	[ACTION_NAMES.LOGIN]: async (event) => {
		const { username, password } = getFormData(await event.request.formData(), loginSchema);

		const [existingUser] = await db
			.select()
			.from(table.user)
			.where(eq(table.user.username, username));
		if (!existingUser) {
			return fail(400, {
				success: false,
				message: 'Incorrect username or password'
			} satisfies ActionResult);
		}

		const validPassword = await verify(existingUser.passwordHash, password, {
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});
		if (!validPassword) {
			return fail(400, {
				success: false,
				message: 'Incorrect username or password'
			} satisfies ActionResult);
		}

		const sessionToken = auth.generateSessionToken();
		const session = await auth.createSession(sessionToken, existingUser.id);
		auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

		return redirect(302, '/dashboard');
	},
	[ACTION_NAMES.REGISTER]: async (event) => {
		const { username, password } = getFormData(await event.request.formData(), registerSchema);

		const userId = generateUserId();
		const passwordHash = await hash(password, {
			// recommended minimum parameters
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});

		try {
			await db.insert(table.user).values({ id: userId, username, passwordHash });

			const sessionToken = auth.generateSessionToken();
			const session = await auth.createSession(sessionToken, userId);
			auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e: unknown) {
			if (e && typeof e === 'object' && 'code' in e && (e as PostgresError).code === '23505') {
				// Postgres unique constraint violation
				return fail(400, {
					success: false,
					message: 'Username already taken'
				} satisfies ActionResult);
			}
			return fail(500, { success: false, message: 'Server error' } satisfies ActionResult);
		}
		return redirect(302, '/dashboard');
	}
};

export const actions: Actions = actionHandlers;

function generateUserId() {
	// ID with 120 bits of entropy, or about the same as UUID v4.
	const bytes = crypto.getRandomValues(new Uint8Array(15));
	const id = encodeBase32LowerCase(bytes);
	return id;
}
